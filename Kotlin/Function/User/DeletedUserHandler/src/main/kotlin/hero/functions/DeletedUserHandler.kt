package hero.functions

import com.github.kittinunf.fuel.httpDelete
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.baseutils.serviceCall
import hero.baseutils.systemEnv
import hero.exceptions.http.HttpStatusException
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.isNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.CancelledByRole
import hero.model.Currency
import hero.model.Path
import hero.model.Post
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.model.topics.RefundMethod
import hero.model.topics.RefundMethod.NONE
import hero.model.topics.RefundMethod.REFUND_IF_NOT_PAID_OUT
import hero.model.topics.SubscriptionCancelRequest
import hero.model.topics.UserDeleted
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import hero.stripe.service.StripeClients
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import org.jooq.DSLContext
import java.time.Instant

@Suppress("unused")
class DeletedUserHandler(
    private val isProduction: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction),
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val internalApiKey: String = SystemEnv.internalApiKey,
) : PubSubSubscriber<UserDeleted>() {
    private val usersCollection = firestore.typedCollectionOf(User)
    private val pathsCollection = firestore.typedCollectionOf(Path)
    private val postsCollection = firestore.typedCollectionOf(Post)
    private val subscribersCollection = firestore.typedCollectionOf(Subscriber)

    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)
    private val subscriptionService: StripeSubscriptionService
    private val stripe: StripeService
    private val stripePaymentMethods: StripePaymentMethodsService

    private val context by lazyContext

    init {
        val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
        stripe = StripeService(stripeClients, pubSub)
        stripePaymentMethods = StripePaymentMethodsService(stripeClients, stripe, pubSub)
        val vatMappingProvider = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD"))
        subscriptionService = StripeSubscriptionService(
            stripeClients,
            stripePaymentMethods,
            isProduction,
            vatMappingProvider.countryToVatMapping(),
        )
    }

    override fun consume(payload: UserDeleted) {
        val user = usersCollection[payload.userId].get()
        val labels = mapOf("userId" to user.id)
        val creatorCurrency = Tier.ofId(user.creator.tierId).currency
        log.info("Cleaning up after user ${user.id}", labels)

        deletePaths(user, labels)
        deleteNotifications(user, labels)
        deletePosts(user)
        cancelSubscriptions(payload, user, labels)
        deleteConnectedAccount(payload, creatorCurrency, user, labels, payload.deletedReason)
        deletePaymentMethods(user, labels, payload.deletedReason)
        // TODO delete subscribers maybe?
        deleteFirebaseUser(user)
    }

    private fun deleteNotifications(
        user: User,
        labels: Map<String, String>,
    ) {
        log.info("Deleting notifications for user ${user.id}", labels)
        val updatedRowsCount = context
            .update(Tables.NOTIFICATION)
            .set(Tables.NOTIFICATION.DELETED_AT, Instant.now())
            .where(Tables.NOTIFICATION.USER_ID.eq(user.id))
            .execute()

        log.info("Marked $updatedRowsCount as deleted for user ${user.id}", labels)
    }

    private fun deletePaths(
        user: User,
        labels: Map<String, String>,
    ) {
        pathsCollection
            .where(Path::userId).isEqualTo(user.id)
            .fetchAll()
            .forEach {
                log.info("Deleting path of $user.id: ${it.id}", labels)
                pathsCollection[it.id].delete()
            }
    }

    private fun deletePosts(user: User) {
        postsCollection
            .where(Post::userId).isEqualTo(user.id)
            .and(Post::messageThreadId).isNull()
            .and(Post::parentId).isNull()
            .and(Post::state).isIn(listOf(PostState.PUBLISHED, PostState.SCHEDULED))
            .fetchAll()
            .takeIf { it.isNotEmpty() }
            ?.forEach { post ->
                postsCollection[post.id].field(Post::state).update(PostState.DELETED)

                // deleting of all related comments and notifications
                pubSub.publish(PostStateChanged(PostStateChange.DELETED, post))
            }
    }

    private fun deletePaymentMethods(
        user: User,
        labels: Map<String, String>,
        reason: String,
    ) {
        user.customerIds.forEach { currencyString, customerId ->
            val currency = Currency.valueOf(currencyString)
            log.info("Deleting all payment methods of ${user.id}.", labels)
            try {
                stripePaymentMethods.deleteAllPaymentMethods(customerId, currency)
            } catch (e: Exception) {
                log.error("Couldn't delete all payment methods of ${user.id}.", labels, cause = e)
            }
            log.info("Deleting all subscriptions of ${user.id}.", labels)
            try {
                subscriptionService.patchSubscriptionsState(
                    customerId = customerId,
                    metaCreatorId = null,
                    cancelAtPeriodEnd = true,
                    currency = currency,
                )
            } catch (e: Exception) {
                log.fatal("Couldn't cancel subscriptions of ${user.id}.", labels, cause = e)
            }
            log.info("Deleting Stripe customer ${user.id}: $customerId", labels)
            try {
                stripe.deleteCustomer(customerId, currency, reason)
            } catch (e: Exception) {
                log.fatal(
                    "Couldn't delete Stripe customer ${user.id}: $customerId",
                    labels,
                    cause = e,
                )
            }
        }
    }

    private fun deleteConnectedAccount(
        payload: UserDeleted,
        currency: Currency,
        user: User,
        labels: Map<String, String>,
        reason: String,
    ) {
        user.creator.stripeAccountId?.let { accountId ->
            log.info("Deleting creator connected account $accountId of user ${user.id}", labels)
            try {
                if (payload.cancelledByRole == CancelledByRole.MODERATOR) {
                    stripe.rejectAccount(accountId, currency, reason)
                } else {
                    stripe.deleteAccount(accountId, currency, reason)
                }
            } catch (e: Exception) {
                log.fatal(
                    "Couldn't delete creator Stripe connected account $accountId of user ${user.id}",
                    labels,
                    cause = e,
                )
            }
        }
    }

    private fun cancelSubscriptions(
        payload: UserDeleted,
        user: User,
        labels: Map<String, String>,
    ) {
        if (payload.cancelSubscriptions && user.creator.stripeAccountId != null) {
            log.info("Cancelling subscription of creator ${user.id}", labels)
            try {
                listOf(Currency.EUR, Currency.USD).forEach {
                    cancelSubscriptionsOfCreator(
                        creator = user,
                        atPeriodEnd = false,
                        cancelledBy = payload.cancelledBy,
                        refundMethod = if (payload.refundSubscriptions) REFUND_IF_NOT_PAID_OUT else NONE,
                        currency = it,
                        labels = labels,
                    )
                }
            } catch (e: Exception) {
                log.fatal("Failed to cancel subscription of creator ${user.id}", labels, cause = e)
            }
        } else {
            log.info("NOT cancelling any subscription of creator ${user.id}", labels)
        }
    }

    private fun deleteFirebaseUser(user: User) {
        if (user.firebaseId == null || user.email == null) {
            log.info("Skipping user ${user.id} deletion in firebase", mapOf("userId" to user.id))
            return
        }

        log.info("Deleting user ${user.id} in firebase", mapOf("userId" to user.id))
        val (_, response) = serviceCall("auth", "/internal/v1/firebase/${user.firebaseId}")
            .httpDelete()
            .header("X-HeroHero-Api-Key" to internalApiKey)
            .response()

        if (response.statusCode >= 400) {
            throw HttpStatusException(response.statusCode, response.responseMessage, mapOf("userId" to user.id), null)
        }
    }

    internal fun cancelSubscriptionsOfCreator(
        creator: User,
        atPeriodEnd: Boolean,
        cancelledBy: String,
        refundMethod: RefundMethod,
        currency: Currency,
        labels: Map<String, String>,
    ) {
        val subscriptions = subscriptionService.getSubscriptionsByCreator(creator.id, false, currency)
        val subsToCancel = subscriptions
            .filter { it.canceledAt == null || it.canceledAt > Instant.now().minusDays(7).epochSecond }

        log.info("Found ${subscriptions.count()} subs and ${subsToCancel.count()} to be cancelled", labels)

        // above, we select also inactive subscriptions so that these can be refunded too
        // if the last try to refund did not succeed - here we filter only those
        // which are few days old
        subsToCancel
            .forEach {
                log.info(
                    "Requesting to cancel subscription ${it.id} $refundMethod refund.",
                    mapOf("userId" to it.metadata["userId"], "creatorId" to creator.id),
                )
                pubSub.publish(
                    SubscriptionCancelRequest(
                        subscriptionId = it.id,
                        atPeriodEnd = atPeriodEnd,
                        cancelledBy = cancelledBy,
                        cancelledByRole = CancelledByRole.MODERATOR,
                        refundMethod = refundMethod,
                        currency = currency,
                    ),
                )
            }
    }
}
