package hero.api.user.controller.dto

import hero.api.subscriber.controller.dto.TierResponse
import hero.api.user.service.UpdateUserProfileImage
import hero.contract.api.dto.CategoryResponse
import hero.core.data.PageRequest
import hero.core.data.SimplePageResponse
import hero.model.Analytics
import hero.model.Creator
import hero.model.GjirafaLivestreamMeta
import hero.model.ImageAsset
import hero.model.NotificationsEnabled
import hero.model.Role

data class UserResponse(
    override val id: String,
    override val name: String,
    override val bio: String,
    override val bioHtml: String,
    override val bioEn: String,
    override val bioHtmlEn: String,
    override val path: String,
    override val image: ImageAsset?,
    override val hasRssFeed: Boolean,
    val counts: UserCountsResponse,
    override val verified: Boolean,
    override val subscribable: Boolean,
    override val tier: TierResponse,
    override val categories: List<CategoryResponse>,
    override val analytics: Analytics,
    val spotify: SpotifyResponse?,
    val privacyPolicyEnabled: Boolean,
    val hasGiftsAllowed: Boolean,
    val isDeleted: Boolean = false,
) : UserPublicDataResponse

data class UserDetailsResponse(
    override val id: String,
    override val name: String,
    override val bio: String,
    override val bioHtml: String,
    override val bioEn: String,
    override val bioHtmlEn: String,
    override val path: String,
    override val image: ImageAsset?,
    override val hasRssFeed: Boolean,
    val counts: UserDetailsCountsResponse,
    override val verified: Boolean,
    override val subscribable: Boolean,
    override val tier: TierResponse,
    override val categories: List<CategoryResponse>,
    override val analytics: Analytics,
    val isOfAge: Boolean,
    val role: Role,
    val email: String?,
    val creator: Creator?,
    val discord: DiscordResponse?,
    val notificationSettings: NotificationsEnabled,
    val language: String,
    val spotify: SpotifyResponse,
    val gjirafaLivestreamMeta: GjirafaLivestreamMeta?,
) : UserPublicDataResponse

data class SpotifyResponse(
    val spotifyUri: String?,
    val hasSpotifyConnection: Boolean,
)

private interface UserPublicDataResponse {
    val id: String
    val name: String
    val bio: String
    val bioHtml: String
    val bioEn: String
    val bioHtmlEn: String
    val path: String
    val image: ImageAsset?
    val hasRssFeed: Boolean
    val verified: Boolean
    val subscribable: Boolean
    val tier: TierResponse
    val categories: List<CategoryResponse>
    val analytics: Analytics
}

data class UserDetailsCountsResponse(
    val supporters: Long,
    val supporting: Long,
    val incomes: Long,
    val incomesClean: Long,
    val payments: Long,
    val posts: Long,
    val invoices: Long,
)

data class UserCountsResponse(
    val supporters: Long,
    val supportersThreshold: Long?,
    val supporting: Long,
    val posts: Long,
)

data class DiscordResponse(
    val id: String,
    val guildId: String?,
)

data class UserDetailsUpdateRequest(
    val path: String,
    val bio: String,
    val name: String,
    val isOfAge: Boolean?,
    val profileImage: UpdateUserProfileImage?,
    val publicEmail: String?,
    val invoiceEmail: String?,
)

data class UpdateAssetUserTimestampRequest(val assetId: String, val timestamp: Double?, val postId: String?)

data class SearchUsersRequest(
    val query: String,
    val pageable: PageRequest? = null,
)

data class PagedUserResponse(
    override val content: List<UserResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<UserResponse>
