package hero.api.user.controller

import hero.api.user.controller.dto.SearchUsersRequest
import hero.api.user.controller.dto.UserDetailsUpdateRequest
import hero.api.user.controller.dto.examplePagedUserResponse
import hero.api.user.controller.dto.exampleSearchUsersRequest
import hero.api.user.controller.dto.exampleUserDetailsResponse
import hero.api.user.controller.dto.exampleUserDetailsUpdateRequest
import hero.api.user.controller.dto.exampleUserResponse
import hero.api.user.controller.dto.toDetailsResponse
import hero.api.user.controller.dto.toResponse
import hero.api.user.service.FindUserByEmail
import hero.api.user.service.GetUser
import hero.api.user.service.GetUserDetails
import hero.api.user.service.SearchUsers
import hero.api.user.service.SearchUsersQueryService
import hero.api.user.service.UpdateUserDetails
import hero.api.user.service.UserCommandService
import hero.api.user.service.UserQueryService
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.toResponse
import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.parseJwtUser
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import hero.model.Role
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path

class UsersController(
    private val userQueryService: UserQueryService,
    private val searchUsersQueryService: SearchUsersQueryService,
    private val userCommandService: UserCommandService,
) {
    @Suppress("Unused")
    val routeGetUserById: ContractRoute =
        ("/v4/users" / Path.userId().of("userId")).get(
            summary = "Get public information about a user/creator",
            tag = "Users",
            parameters = object {},
            responses = listOf(Status.OK example exampleUserResponse),
            handler = { request, _, userId ->
                val user = request.parseJwtUser()
                val result = userQueryService.execute(GetUser(userId))
                val response = result.toResponse()
                    .let {
                        if (user?.id == userId) {
                            val counts = it.counts.copy(
                                supporters = result.user.counts.supporters,
                                supportersThreshold = null,
                            )
                            it.copy(counts = counts)
                        } else {
                            it
                        }
                    }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routeSearchUsers: ContractRoute =
        ("/v4/users/search").post(
            summary = "Get public information about a user/creator",
            tag = "Users",
            parameters = object {},
            responses = listOf(Status.OK example examplePagedUserResponse),
            receiving = exampleSearchUsersRequest,
            handler = { request, _ ->
                val user = request.parseJwtUser()
                val body = lens<SearchUsersRequest>(request)
                val pageable = body.pageable ?: PageRequest()

                val result = if (user?.roleIndex == Role.MODERATOR.ordinal && "@" in body.query) {
                    val foundUser = userQueryService.execute(FindUserByEmail(body.query))
                    Page(listOfNotNull(foundUser), PageRequest(), false)
                } else {
                    searchUsersQueryService.execute(SearchUsers(body.query, user?.id, pageable))
                }

                val response = result.toResponse { it.toResponse(listOf()) }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routeGetUserDetails: ContractRoute =
        ("/v4/users" / Path.userId().of("userId") / "details").get(
            summary = "Get private information about a user/creator",
            tag = "Users",
            parameters = object {},
            responses = listOf(Status.OK example exampleUserDetailsResponse),
            handler = { request, _, userId, _ ->
                val user = request.getJwtUser()
                if (user.id != userId) {
                    throw ForbiddenException("Cannot request user id of another user")
                }
                val result = userQueryService.execute(GetUserDetails(user.id))

                Response(Status.OK).body(result.toDetailsResponse())
            },
        )

    @Suppress("Unused")
    val routeUpdateUserDetails: ContractRoute =
        ("/v4/users" / Path.userId().of("userId") / "details").put(
            summary = "Update private information about a user/creator",
            tag = "Users",
            parameters = object {},
            responses = listOf(Status.OK example exampleUserDetailsResponse),
            receiving = exampleUserDetailsUpdateRequest,
            handler = { request, _, userId, _ ->
                val user = request.getJwtUser()
                if (user.id != userId) {
                    throw ForbiddenException("Cannot request user id of another user")
                }
                val body = lens<UserDetailsUpdateRequest>(request)

                userCommandService.execute(
                    UpdateUserDetails(
                        user.id,
                        path = body.path,
                        bio = body.bio,
                        name = body.name,
                        isOfAge = body.isOfAge,
                        profileImage = body.profileImage,
                        publicEmail = null,
                        invoiceEmail = null,
                    ),
                )
                val result = userQueryService.execute(GetUserDetails(user.id))

                Response(Status.OK).body(result.toDetailsResponse())
            },
        )
}
