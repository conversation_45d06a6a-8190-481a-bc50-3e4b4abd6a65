package hero.api.user.controller.dto

import hero.api.subscriber.controller.dto.TierResponse
import hero.api.user.service.UpdateUserProfileImage
import hero.contract.api.dto.CategoryResponse
import hero.core.data.PageRequest
import hero.model.Analytics
import hero.model.Creator
import hero.model.Currency
import hero.model.GjirafaLivestreamMeta
import hero.model.ImageAsset
import hero.model.NotificationsEnabled
import hero.model.Role
import hero.model.StripeRequirements

private val exampleAnalytics = Analytics(
    facebookPixelId = "facebook-pixel-id",
    ga4Stream = "ga-4-stream",
    googleAdsConversionId = "google-ads-conversion-id",
    googleAdsConversionLabel = "google-ads-conversion-label",
    tiktokPixelId = "tiktok-pixel-id",
    leadHub = "lead-hub",
)

val exampleUserResponse = UserResponse(
    id = "albxaceqjpbcb",
    name = "pecoj46789",
    bio = "random-bio",
    bioHtml = "<strong>random-bio</strong>",
    bioEn = "random-bio-in-en",
    bioHtmlEn = "<strong>random-bio-in-en</strong>",
    path = "principledalbxaceqjpbcb",
    image = ImageAsset(
        id = "https://heroheroco-assets-devel.storage.googleapis.com/images/user/herotesterznwnkkwc/1618164526274.png",
        width = 0,
        height = 0,
        hidden = false,
    ),
    hasRssFeed = false,
    counts = UserCountsResponse(
        supporters = 1,
        supportersThreshold = 100,
        supporting = 3,
        posts = 10,
    ),
    verified = true,
    subscribable = true,
    tier = TierResponse(
        id = "EUR05",
        priceCents = 500,
        currency = Currency.EUR,
        default = true,
        hidden = false,
    ),
    categories = listOf(
        CategoryResponse(id = "category-id", name = "ELECTRIC PROCESS", slug = "electric-process"),
    ),
    privacyPolicyEnabled = true,
    publicEmail = "<EMAIL>",
    analytics = exampleAnalytics,
    hasGiftsAllowed = false,
    spotify = SpotifyResponse("spotify-uri", true),
)

val exampleUserDetailsResponse = UserDetailsResponse(
    id = "albxaceqjpbcb",
    name = "pecoj46789",
    bio = "random-bio",
    bioHtml = "<strong>random-bio</strong>",
    bioEn = "random-bio-in-en",
    bioHtmlEn = "<strong>random-bio-in-en</strong>",
    path = "principledalbxaceqjpbcb",
    image = ImageAsset(
        id = "https://heroheroco-assets-devel.storage.googleapis.com/images/user/herotesterznwnkkwc/1618164526274.png",
        width = 0,
        height = 0,
        hidden = false,
    ),
    hasRssFeed = false,
    counts = UserDetailsCountsResponse(
        supporters = 1,
        supporting = 3,
        posts = 10,
        payments = 50,
        incomesClean = 60,
        incomes = 70,
        invoices = 30,
    ),
    verified = true,
    subscribable = true,
    tier = TierResponse(
        id = "EUR05",
        priceCents = 500,
        currency = Currency.EUR,
        default = true,
        hidden = false,
    ),
    categories = listOf(
        CategoryResponse(id = "category-id", name = "ELECTRIC PROCESS", slug = "electric-process"),
    ),
    email = "<EMAIL>",
    creator = Creator(
        tierId = "EUR05",
        stripeAccountActive = true,
        stripeAccountId = "stripe-account-id",
        stripeAccountLegacyIds = listOf(),
        stripeAccountOnboarded = false,
        suspended = false,
        currency = Currency.CZK,
        stripeRequirements = StripeRequirements(
            stripeAccountId = "stripe-account-id",
            deleted = false,
            disabledReason = "reason",
            currentlyDue = listOf(),
            eventuallyDue = listOf(),
            pastDue = listOf(),
            errors = listOf(),
            pendingVerification = listOf(),
        ),
    ),
    discord = DiscordResponse("discord-id", "guild-id"),
    notificationSettings = NotificationsEnabled(true),
    language = "cs",
    role = Role.USER,
    analytics = exampleAnalytics,
    spotify = SpotifyResponse("spotify-uri", false),
    gjirafaLivestreamMeta = GjirafaLivestreamMeta(
        publicId = "public-id",
        streamUrl = "stream-url",
        streamKey = "stream-key",
        playbackUrl = "playback-url",
    ),
    isOfAge = false,
)

val exampleUserDetailsUpdateRequest = UserDetailsUpdateRequest(
    path = "franta",
    bio = "bio",
    name = "Franta Novotny",
    isOfAge = true,
    profileImage = UpdateUserProfileImage("image-url", 500, 400),
    publicEmail = "<EMAIL>",
    invoiceEmail = "<EMAIL>",
)

val exampleUpdateAssetUserTimestampRequest = UpdateAssetUserTimestampRequest(
    assetId = "vjsnpjvn",
    timestamp = 493.1,
    postId = "post-id",
)

val exampleSearchUsersRequest = SearchUsersRequest(
    query = "franta",
    pageable = PageRequest(),
)

val examplePagedUserResponse = PagedUserResponse(
    content = listOf(exampleUserResponse),
    hasNext = true,
    afterCursor = "after-cursor",
    beforeCursor = "before-cursor",
)
