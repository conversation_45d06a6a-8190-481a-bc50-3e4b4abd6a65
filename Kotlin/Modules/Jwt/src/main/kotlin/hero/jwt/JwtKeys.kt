package hero.jwt

import hero.jwt.TokenType.ACCESS
import io.jsonwebtoken.Claims
import io.jsonwebtoken.JwtParser
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.jackson.io.JacksonDeserializer
import io.jsonwebtoken.jackson.io.JacksonSerializer
import java.security.Key
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.time.Instant
import java.util.Date
import javax.crypto.SecretKey

/*
# generate a 2048-bit RSA private key
openssl genrsa -out build/private_key.pem 2048
# generate public key for clients
openssl rsa -in build/private_key.pem -pubout > src/main/resources/static/jwt-public.pem
# convert public key for internal use
openssl rsa -pubin -inform PEM -in src/main/resources/static/jwt-public.pem -outform DER -out src/main/resources/jwt-public.der
# convert private Key to PKCS#8 format (so Java can read it)
openssl pkcs8 -topk8 -inform PEM -outform DER -in build/private_key.pem -out src/main/resources/jwt-private.der -nocrypt
*/

private val classLoaderTarget = object {}

private val kf = KeyFactory.getInstance("RSA")

val classLoader = classLoaderTarget::class.java.classLoader

private val heroPrivateKeyBytes =
    classLoader.getResourceAsStream("jwt-private.der")?.readAllBytes()
        ?: error("Cannot read jwt-private.der from classpath.")
private val heroPrivateKeySpec = PKCS8EncodedKeySpec(heroPrivateKeyBytes)

private val heroPublicKeyBytes =
    classLoader.getResourceAsStream("jwt-public.der")?.readAllBytes()
        ?: error("Cannot read jwt-public.der from classpath.")
private val heroPublicKeySpec = X509EncodedKeySpec(heroPublicKeyBytes)

val heroPrivateKey: PrivateKey = kf.generatePrivate(heroPrivateKeySpec)
val heroPublicKey: PublicKey = kf.generatePublic(heroPublicKeySpec)

fun Map<String, Any?>.toJwt(key: Key = heroPrivateKey): String =
    Jwts.builder()
        // we have to set the instance of serializer because of this issue
        // https://github.com/jwtk/jjwt/issues/873#issuecomment-1830501040
        .json(JacksonSerializer())
        .claims(this)
        .signWith(key)
        .compact()

fun jwtParser(publicKey: PublicKey = heroPublicKey): JwtParser =
    Jwts
        .parser()
        .json(JacksonDeserializer())
        .verifyWith(publicKey)
        .build()

fun jwtParser(secretKey: SecretKey): JwtParser =
    Jwts
        .parser()
        .json(JacksonDeserializer())
        .verifyWith(secretKey)
        .build()

fun String.parseJwt(secretKey: SecretKey): Claims = (jwtParser(secretKey).parseSignedClaims(this).payload as Claims)

fun String.parseJwt(publicKey: PublicKey = heroPublicKey): Claims =
    // exceptions should not be handled here - currently all handled in HTTP4K
    (jwtParser(publicKey).parseSignedClaims(this).payload as Claims)

data class JwtUser(
    val id: String,
    val expiry: Long,
    val roleIndex: Int,
    val sessionId: String? = null,
)

fun JwtUser.toJwt(
    key: Key = heroPrivateKey,
    type: TokenType = ACCESS,
) = generateJwt(
    userId = id,
    expiry = expiry,
    type = type,
    additionalClaims = buildMap {
        put(ROLE_CLAIM, roleIndex)
        if (sessionId != null) {
            put(SESSION_ID, sessionId)
        }
    },
    key = key,
)

fun generateJwt(
    userId: String,
    expiry: Long,
    type: TokenType,
    additionalClaims: Map<String, Any> = mapOf(),
    key: Key = heroPrivateKey,
    issuedAt: Instant = Instant.now(),
): String {
    val claims: Claims = Jwts.claims()
        .subject(userId)
        .expiration(Date.from(Instant.ofEpochSecond(expiry)))
        .issuedAt(Date.from(issuedAt))
        .apply {
            add(TOKEN_TYPE_CLAIM, type)
            add(additionalClaims)
        }
        .build()

    return Jwts.builder()
        .claims(claims)
        .issuer("herohero")
        .signWith(key)
        .compact()
}

private const val JWT_VALIDITY_SECONDS: Long = 3 * 60

fun String.authorization(sessionId: String? = null): String {
    val jwtUser = JwtUser(this, Instant.now().plusSeconds(JWT_VALIDITY_SECONDS).epochSecond, 0, sessionId)
    return jwtUser.toJwt()
}

const val ROLE_CLAIM = "ro"
const val SESSION_ID = "session_id"
const val TOKEN_TYPE_CLAIM = "type"

enum class TokenType {
    ACCESS,
    IMPERSONATE,
    REFRESH,
    DELETE,
    VERIFY_EMAIL,
}

const val IMPERSONATION_TOKEN = "impersonateToken2"
const val ACCESS_TOKEN = "accessToken2"
const val REFRESH_TOKEN = "refreshToken2"
