package hero.model

import com.fasterxml.jackson.annotation.JsonProperty
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant
import java.util.UUID

@NoArg
data class Notification(
    val userId: String,
    val type: NotificationType,
    val actorIds: List<String> = listOf(),
    val objectType: StorageEntityType,
    val objectId: String? = null,
    val created: Instant,
    val timestamp: Instant,
    val seenAt: Instant? = null,
    val checkedAt: Instant? = null,
    val id: String = "$userId-${UUID.randomUUID().toString().replace("-", "")}",
) {
    companion object : EntityCollection<Notification> {
        override val collectionName: String = "notifications"
    }
}

enum class NotificationType {
    @JsonProperty("new_subscription")
    NEW_SUBSCRIPTION,
    @JsonProperty("cancelled_subscription_ended")
    CANCELLED_SUBSCRIPTION_ENDED,
    @JsonProperty("cancelled_subscription_insufficient_funds")
    CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS,
    @JsonProperty("cancelled_subscription_refused")
    CANCELLED_SUBSCRIPTION_REFUSED,
    @JsonProperty("cancelled_subscription_refunded")
    CANCELLED_SUBSCRIPTION_REFUNDED,
    @JsonProperty("cancelled_subscription_by_creator")
    CANCELLED_SUBSCRIPTION_BY_CREATOR,
    @JsonProperty("cancelled_subscription_other")
    CANCELLED_SUBSCRIPTION_OTHER,
    @JsonProperty("new_post")
    NEW_POST,
    @JsonProperty("new_livestream")
    NEW_LIVESTREAM,
    @JsonProperty("new_comment")
    NEW_COMMENT,
    @JsonProperty("new_reply")
    NEW_REPLY,
    @JsonProperty("new_reply_to_reply")
    NEW_REPLY_TO_REPLY,
    @JsonProperty("paid_post")
    PAID_POST,
    @JsonProperty("payment_card_declined")
    PAYMENT_CARD_DECLINED,
    @JsonProperty("payment_insufficient_funds")
    PAYMENT_INSUFFICIENT_FUNDS,
    @JsonProperty("payment_failed")
    PAYMENT_FAILED,
    @JsonProperty("newsletter")
    NEWSLETTER,
    @JsonProperty("terms_changed")
    TERMS_CHANGED,
}

data class NotificationDisableRequest(
    val userId: String,
    val notificationType: NotificationType,
)
