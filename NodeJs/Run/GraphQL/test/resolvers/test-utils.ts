import { NotificationAPI } from '../../src/datasources/NotificationAPI'
import { PostAPI } from '../../src/datasources/PostAPI'
import { UserAPI } from '../../src/datasources/UserAPI'
import { MessageThreadAPI } from '../../src/datasources/MessageThreadAPI'
import { CommentModel, ParentModel, PostAssetModel, PostModel, SavedPostModel } from '../../src/models/post'
import {
    Currency,
    GjirafaLivestreamStatus,
    GjirafaQualityTypeStatus,
    PostDocumentType,
    PostState,
    SubscriptionCouponMethod,
    SubscriptionRelationType,
    SubscriptionStatus,
    SubscriptionType,
    UserRole,
} from '../../src/generated/resolvers-types'
import { MessageModel, MessageThreadDetailsModel, MessageThreadModel } from '../../src/models/message-thread'
import { CategoryModel, UserAnalyticsModel, UserDetailsModel, UserModel } from '../../src/models/user'
import { NotificationModel, NotificationType } from '../../src/models/notification'
import { DataSourceContext } from '../../src/context'
import { schema } from '../../src/resolvers'
import { ApolloServer } from '@apollo/server'
import { SubscriptionAPI } from '../../src/datasources/SubscriptionAPI'
import { FullSubscriptionModel, LimitedSubscriptionModel, SubscribeRequestModel } from '../../src/models/subscription'
import { LibraryAPI } from '../../src/datasources/LibraryAPI'
import { StorageEntityType } from '../../src/generated/api'
import { securityPlugins, validationRules } from '../../src/plugins/security'
import { UserJwtRole } from '../../src/auth'
import { GjirafaAPI } from '../../src/datasources/GjirafaAPI'
import { StatisticsAPI } from '../../src/datasources/StatisticsAPI'
import { UserMediaStoreAPI } from '../../src/datasources/UserMediaStoreAPI'
import { CategoriesAPI } from '../../src/datasources/CategoriesAPI'
import { NotificationSettingsAPI } from '../../src/datasources/NotificationSettingsAPI'
import { MediaAPI } from '../../src/datasources/MediaAPI'
import { SubscribeRequestAPI } from '../../src/datasources/SubscribeRequestAPI'
import { SessionAPI } from '../../src/datasources/SessionAPI'

export const testContext = (config?: { userId?: string; role?: UserJwtRole }): DataSourceContext => ({
    dataSources: {
        notificationAPI: {} as NotificationAPI,
        notificationSettingsAPI: {} as NotificationSettingsAPI,
        postAPI: {} as PostAPI,
        userAPI: {} as UserAPI,
        messageThreadAPI: {} as MessageThreadAPI,
        mediaAPI: {} as MediaAPI,
        subscriptionAPI: {} as SubscriptionAPI,
        subscribeRequestAPI: {} as SubscribeRequestAPI,
        libraryAPI: {} as LibraryAPI,
        gjirafaAPI: {} as GjirafaAPI,
        statisticsAPI: {} as StatisticsAPI,
        userMediaStoreAPI: {} as UserMediaStoreAPI,
        categoriesAPI: {} as CategoriesAPI,
        sessionAPI: {} as SessionAPI,
    },
    ...(config && {
        user: {
            id: config.userId ?? 'user-id',
            role: config.role ?? 'user',
            cookieExpiration: date(),
        },
    }),
})

function date() {
    const date = new Date()
    date.setDate(date.getDate() + 1)

    return date
}

export const post = (config?: {
    id?: string
    fullAssets?: boolean
    userId?: string
    savedPostInfo?: boolean
    textHtml?: string
    textDelta?: string
    text?: string
    pinnedAt?: string
    publishedAt?: string
    isSponsored?: boolean
    isAgeRestricted?: boolean
    isExcludedFromRss?: boolean
    categories?: CategoryModel[]
    assets?: PostAssetModel[]
}): PostModel => ({
    id: config?.id ?? 'post-id',
    userId: config?.userId ?? 'user-id',
    publishedAt: config?.publishedAt ?? '2021-02-26T19:21:45.441Z',
    pinnedAt: config?.pinnedAt,
    text: config?.text,
    textHtml: config?.textHtml,
    isExcludedFromRss: config?.isExcludedFromRss ?? false,
    textDelta: config?.textDelta,
    assets: config?.assets ?? [
        {
            width: 150,
            url: 'image-url',
            height: 100,
            assetType: 'image',
        },
        {
            url: 'document-url',
            type: PostDocumentType.DOCX,
            name: 'document-name',
            assetType: 'document',
        },
        {
            status: GjirafaQualityTypeStatus.COMPLETE,
            videoStreamUrl: 'gjirafa-video-url',
            audioStaticUrl: 'gjirafa-audio-static-url',
            audioStreamUrl: 'gjirafa-audio-stream-url',
            thumbnailUrl: 'gjirafa-thumbnail-url',
            width: 450,
            height: 50,
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            keyId: '',
            id: 'id',
            duration: 45.1,
            assetType: 'gjirafa',
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
        },
        {
            assetType: 'empty',
        },
    ],
    categories: config?.categories ?? [
        {
            id: 'category-id',
            name: 'category-name',
            slug: 'category-slug',
        },
    ],
    fullAssets: config?.fullAssets ?? true,
    state: PostState.DELETED,
    counts: {
        comments: 10,
        replies: 10,
    },
    ...(config?.savedPostInfo && {
        savedPostInfo: {
            id: 'saved-post-id',
            savedAt: '2023-09-18T00:00:00Z',
        },
    }),
    isSponsored: config?.isSponsored ?? true,
    isAgeRestricted: config?.isAgeRestricted ?? false,
})

export const postAsset = (
    assetType: 'document' | 'image' | 'gjirafa' | 'gjirafa-livestream' | 'empty'
): PostAssetModel => {
    if (assetType === 'document') {
        return {
            url: 'document-url',
            type: PostDocumentType.DOCX,
            name: 'doucment-name',
            thumbnailUrl: 'thumbnail-url',
            assetType: 'document',
        }
    } else if (assetType === 'image') {
        return {
            url: 'image-url',
            width: 900,
            height: 1000,
            assetType: 'image',
        }
    } else if (assetType === 'gjirafa') {
        return {
            hasAudio: true,
            hasVideo: true,
            keyId: '',
            id: 'gjirafa-id',
            hidden: false,
            duration: 100,
            height: 50,
            width: 100,
            status: GjirafaQualityTypeStatus.COMPLETE,
            thumbnailUrl: 'thumbnnail-url',
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
            assetType: 'gjirafa',
        }
    } else if (assetType === 'gjirafa-livestream') {
        return {
            id: 'vdjnejr',
            status: GjirafaLivestreamStatus.LIVE,
            channelPublicId: 'adqmnej',
            playbackUrl: 'playback-url',
            assetType: 'gjirafa-livestream',
        }
    } else {
        return {
            assetType: 'empty',
        }
    }
}

export const comment = (config?: {
    id?: string
    fullAssets?: boolean
    userId?: string
    parent?: ParentModel
    text?: string
    textHtml?: string
}): CommentModel => ({
    id: config?.id ?? 'comment-id',
    userId: config?.userId ?? 'user-id',
    publishedAt: '2021-02-26T19:21:45.441Z',
    parentId: 'parent-id',
    text: config?.text,
    textHtml: config?.textHtml,
    assets: [
        {
            width: 150,
            url: 'image-url',
            height: 100,
            assetType: 'image',
        },
        {
            url: 'document-url',
            type: PostDocumentType.DOCX,
            name: 'document-name',
            assetType: 'document',
        },
        {
            status: GjirafaQualityTypeStatus.COMPLETE,
            videoStreamUrl: 'gjirafa-video-url',
            audioStaticUrl: 'gjirafa-audio-static-url',
            audioStreamUrl: 'gjirafa-audio-stream-url',
            thumbnailUrl: 'gjirafa-thumbnail-url',
            width: 450,
            height: 50,
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            keyId: '',
            id: 'id',
            duration: 45.1,
            assetType: 'gjirafa',
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
        },
        {
            assetType: 'empty',
        },
    ],
    state: PostState.DELETED,
    ...(config?.parent && { parent: config.parent }),
    counts: {
        comments: 10,
        replies: 10,
    },
})

export const message = (id: string): MessageModel => ({
    id: id,
    messageThreadId: 'message-thread-id',
    sentById: 'sender-id',
    assets: [
        {
            width: 150,
            url: 'image-url',
            height: 100,
            assetType: 'image',
        },
        {
            url: 'document-url',
            type: PostDocumentType.DOCX,
            name: 'document-name',
            assetType: 'document',
        },
        {
            status: GjirafaQualityTypeStatus.COMPLETE,
            videoStreamUrl: 'gjirafa-video-url',
            audioStaticUrl: 'gjirafa-audio-static-url',
            audioStreamUrl: 'gjirafa-audio-stream-url',
            thumbnailUrl: 'gjirafa-thumbnail-url',
            width: 450,
            height: 50,
            hidden: false,
            hasAudio: true,
            hasVideo: true,
            keyId: '',
            id: 'id',
            duration: 45.1,
            assetType: 'gjirafa',
            previewStaticUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
            previewAnimatedUrl:
                'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
            previewStripUrl:
                'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
        },
        {
            assetType: 'empty',
        },
    ],
})

export const user = (id: string, analytics?: UserAnalyticsModel, bio?: string, spotifyShowUri?: string): UserModel => ({
    id: id,
    name: 'user-name',
    bio: bio ?? 'user-bio',
    path: 'user-path',
    subscribable: true,
    verified: true,
    spotifyShowUri: spotifyShowUri,
    counts: {
        supporting: 10,
        supporters: 50,
        supportersThreshold: 50,
        posts: 40,
    },
    hasRssFeed: true,
    tier: {
        id: 'EUR05',
        priceCents: 500,
        hidden: false,
        default: false,
        currency: Currency.EUR,
    },
    categories: [
        {
            id: 'category-id',
            name: 'category-name',
            slug: 'slug',
        },
    ],
    isDeleted: false,
    privacyPolicyEnabled: false,
    analytics,
    hasGiftsAllowed: false,
})

export const userDetails = (config: {
    id: string
    language?: string
    profilePicture?: { url: string; width: number; height: number; hidden: boolean } | null
    bio?: string
    isOfAge?: boolean
}): UserDetailsModel => ({
    id: config.id,
    name: 'user-name',
    bio: config.bio ?? 'user-bio',
    image:
        config.profilePicture ??
        (config.profilePicture === undefined
            ? {
                  url: 'image-id',
                  width: 420,
                  height: 69,
                  hidden: true,
              }
            : null),
    path: 'user-path',
    subscribable: true,
    verified: false,
    counts: {
        supporting: 150,
        supporters: 21231,
        posts: 243,
        payments: 300,
        invoices: 121,
        incomesClean: 421,
        incomes: 21,
    },
    hasRssFeed: true,
    tier: {
        id: 'EUR05',
        priceCents: 500,
        hidden: false,
        default: false,
        currency: Currency.EUR,
    },
    categories: [
        {
            id: 'category-id',
            name: 'category-name',
            slug: 'slug',
        },
    ],
    language: config.language ?? 'cs',
    discord: {
        id: 'discord-id',
        guildId: 'guild-id',
    },
    creator: {
        stripeAccountActive: true,
        stripeAccountOnboarded: false,
        stripeAccountId: 'stripe-account-id',
    },
    email: 'user-email',
    notificationSettings: {
        emailNewPost: false,
    },
    role: UserRole.MODERATOR,
    livestream: {
        streamKey: 'stream-key',
        streamUrl: 'stream-url',
        publicId: 'public-id',
    },
    isOfAge: config.isOfAge ?? true,
})

export const limitedSubscription = (config?: {
    id?: string
    subscribedAt?: string
    tierId?: string
    type?: SubscriptionType
    couponAppliedForMonths?: number
    status?: SubscriptionStatus
    cancelAtPeriodAt?: boolean
}): LimitedSubscriptionModel => ({
    id: config?.id ?? 'subscription-id',
    subscribedAt: config?.subscribedAt ?? '2023-05-11T12:12:11.844280Z',
    creator: user('creator-id'),
    subscriber: user('subscriber-id'),
    subscriptionModelType: 'limited',
})

export const fullSubscription = (config?: {
    id?: string
    subscribedAt?: string
    tierId?: string
    type?: SubscriptionType
    couponAppliedForMonths?: number
    status?: SubscriptionStatus
    cancelAtPeriodAt?: boolean
}): FullSubscriptionModel => ({
    id: config?.id ?? 'subscription-id',
    expires: '2023-05-10T12:12:11.844280Z',
    couponAppliedForMonths: config?.couponAppliedForMonths ?? 5,
    couponMethod: SubscriptionCouponMethod.TRIAL,
    couponPercentOff: 50,
    status: config?.status ?? SubscriptionStatus.ACTIVE,
    type: config?.type ?? SubscriptionType.STRIPE,
    cancelAtPeriodEnd: config?.cancelAtPeriodAt ?? false,
    subscribedAt: config?.subscribedAt ?? '2023-05-11T12:12:11.844280Z',
    creator: user('creator-id'),
    subscriber: user('subscriber-id'),
    tier: {
        id: config?.tierId ?? 'EUR05',
        priceCents: 500,
        currency: Currency.EUR,
        hidden: false,
        default: false,
    },
    subscriptionModelType: 'full',
})

export const notification = (config?: {
    id?: string
    lastActor?: UserModel
    lastActorId?: string
    objectId?: string
    objectType?: StorageEntityType
    type?: NotificationType
}): NotificationModel => ({
    id: config?.id ?? 'notification-id',
    createdAt: '2021-02-27T19:21:45.441Z',
    type: config?.type ?? NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
    lastActorId: config?.lastActor?.id ?? config?.lastActorId ?? 'last-actor-id-1',
    objectId: config?.objectId ?? 'target-user-id-1',
    objectType: config?.objectType ?? StorageEntityType.POST,
    checkedAt: '2023-10-06T06:33:10.926Z',
    seenAt: '2022-01-04T07:21:32.135Z',
    actorCount: 10,
    lastActor: config?.lastActor,
})

export const category = (config?: { id?: string; name?: string; slug?: string }): CategoryModel => ({
    id: config?.id ?? 'id',
    name: config?.name ?? 'name',
    slug: config?.slug ?? 'slug',
})

export const savedPost = (id: string): SavedPostModel => ({
    id,
    savedAt: '2023-10-06T06:33:10.926Z',
    post: post({ id }),
})

export const messageThread = (id: string): MessageThreadModel => ({
    id,
    participantIds: ['hunghoangzfgvmdem', 'eliskaslaharovanojvddav'],
    participants: [user('hunghoangzfgvmdem'), user('eliskaslaharovanojvddav')],
    createdAt: '2023-05-10T12:12:11.844280Z',
    seenAt: '2023-10-30T17:10:47.109783Z',
    checkedAt: '2023-11-30T17:10:47.109783Z',
    lastMessageAt: '2023-12-30T17:10:47.109783Z',
    lastMessage: message('last-message-id'),
})

export const messageThreadDetails = (id: string): MessageThreadDetailsModel => ({
    id,
    participantIds: ['hunghoangzfgvmdem', 'eliskaslaharovanojvddav'],
    participants: [user('hunghoangzfgvmdem'), user('eliskaslaharovanojvddav')],
    createdAt: '2023-05-10T12:12:11.844280Z',
    canPost: true,
    commonCreators: [''],
    relation: SubscriptionRelationType.GROUP,
    seenAt: '2023-10-30T17:10:47.109783Z',
    checkedAt: '2023-11-30T17:10:47.109783Z',
    lastMessageAt: '2023-12-30T17:10:47.109783Z',
    deletedAt: undefined,
    deleted: false,
    archived: false,
    lastMessage: message('last-message-id'),
})

export const subscribeRequest = (config?: {
    id?: number
    userId?: string
    creatorId?: string
    createdAt?: string
    acceptedAt?: string | null
    declinedAt?: string | null
    deletedAt?: string | null
}): SubscribeRequestModel => ({
    id: config?.id ?? 123,
    userId: config?.userId ?? 'user-id',
    creatorId: config?.creatorId ?? 'creator-id',
    createdAt: config?.createdAt ?? '2023-09-17T00:00:00Z',
    acceptedAt: config?.acceptedAt ?? null,
    declinedAt: config?.declinedAt ?? null,
    deletedAt: config?.deletedAt ?? null,
})

export const testApolloServer = new ApolloServer<DataSourceContext>({
    schema,
    plugins: [...securityPlugins],
    validationRules: [...validationRules],
    includeStacktraceInErrorResponses: false,
})
