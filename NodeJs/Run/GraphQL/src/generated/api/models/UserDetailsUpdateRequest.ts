/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { UpdateUserProfileImage } from './UpdateUserProfileImage';
export type UserDetailsUpdateRequest = {
    path: string;
    bio: string;
    name: string;
    isOfAge?: boolean | null;
    profileImage?: UpdateUserProfileImage;
    publicEmail?: string | null;
    invoiceEmail?: string | null;
};

