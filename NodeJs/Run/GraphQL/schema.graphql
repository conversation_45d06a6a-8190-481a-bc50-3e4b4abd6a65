type Query {
    """
    List of notifications objects of the current user.
    """
    notifications(
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come before the specified cursor."
        before: String
        "Returns the elements that come after the specified cursor."
        after: String
        "Filter returned notifications"
        filter: NotificationFilter
    ): NotificationConnection!

    """
    Returns notification settings for currently logged in user
    """
    notificationSettings: NotificationSettings!

    """
    List of subscribe requests for the current user.
    """
    subscribeRequests(
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come after the specified cursor."
        after: String
    ): SubscribeRequestConnection!

    """
    List of active message threads of the current user.

    Be aware that this query will **never return full assets** in `lastMessage`, also
    fields `canPost`, `relation` and `commonCreators` will be null, and
    an empty array respectively. To get this information, fetch single message thread instead.

    Sorted in descending order of recency.
    """
    messageThreads(
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come after the specified cursor."
        after: String
    ): MessageThreadConnection!

    """
    Retrieves specific message thread with details that are not available through `messageThreads` query.
    """
    messageThreadDetails(id: ID!): MessageThreadDetails!

    """
    Retrieves messages from given message thread.

    Sorted in descending order of recency.
    """
    messages(
        "Message thread id"
        id: ID!
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come after the specified cursor."
        after: String
    ): MessageConnection!

    """
    One specific message
    """
    message(id: ID!): Message!

    """
    Creator's posts

    Sorted in descending order of recency.
    """
    posts(
        "Creator id"
        id: ID @deprecated(reason: "Use filter creatorId instead, this will be ignored now")
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come after the specified cursor."
        after: String
        "Filter returned posts"
        filter: PostFilter
        "Sort returned posts"
        sort: PostSort
    ): PostConnection!

    """
    This query does not paginate yet
    """
    searchPosts(filter: SearchPostsFilter): PostConnection!

    """
    Returns creator's livestreams that are currently live
    """
    livestreams: PostConnection!

    """
    One specific creator's post
    """
    post(id: ID!): Post!

    """
    Comments for a post or another comment

    Sorted in descending order of recency.
    """
    comments(
        "Post or comment id"
        id: ID!
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come before the specified cursor."
        before: String
        "Returns the elements that come after the specified cursor."
        after: String
        "Direction of sorting."
        sortDirection: SortDirection
    ): CommentConnection!

    """
    Fetch single comment. This query should be only used from notification tab when clicking through to the comment
    that triggered the notification.
    """
    comment("Comment id" id: ID!): Comment!

    """
    One specific user
    """
    user("Either a path or an id of the user" id: ID!): User!

    """
    Return details of the user, must be authenticated as moderator
    """
    adminUserDetails(id: ID!): UserDetails!

    """
    The currently authenticated user
    """
    viewer: UserDetails!

    """
    Subscribers of given creator
    """
    subscribers(
        creatorId: ID!
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come after the specified cursor."
        after: String
        "By which field should the pagination order by. Default is NEWEST"
        orderBy: SubscriptionOrderBy! = NEWEST
    ): SubscriptionConnection!

    """
    Subscriptions of given user
    """
    subscriptions(
        userId: ID!
        "Returns up to the first n elements from the list."
        first: Int
        "Returns the elements that come after the specified cursor."
        after: String
        "By which field should the pagination order by. Default is NEWEST"
        orderBy: SubscriptionOrderBy! = NEWEST
        "Subscription filtering options"
        filter: SubscriptionFilter
    ): SubscriptionConnection!

    """
    Search users by given query. Paging is not supported at the moment.
    """
    searchUsers(query: String!, first: Int): UserConnection!

    """
    Fetch popular creators. Paging is not supported at the moment.
    Locale to fetch popular creators from.
    """
    featuredCreators(locale: String, first: Int): UserConnection!

    """
    Returns 20 creators from given category. The result is randomized.
    """
    featuredCreatorsRandomized(featuredCategory: FeaturedCategories!): RandomizedFeaturedCreatorsPayload!

    """
    Fetch saved posts in user's library. Default behaviour is that posts from both subscribed and unsubscribed creators
    are returned, if you want to modify the behaviour, use filter.
    Default and only sort is by `savedAt DESC`.
    """
    savedPosts(first: Int, after: String, filter: SavedPostsFilter): SavedPostConnection!

    """
    Use this to get sizes of all quality types for given video asset. For example if you need a size for 420p stream.
    """
    gjirafaVideoQualities(assetId: ID!): [GjirafaAssetQuality!]!

    """
    Fetch viewer's expected income.
    """
    expectedIncome: ExpectedIncome!
}

type Mutation {
    """
    Update user's timestamp for given asset id.
    If the timestamp is null, the asset is removed from in progress watch state.
    """
    assetTimestampUpdate(
        assetId: ID!
        timestamp: Float

        """
        Always pass this, since assetId does not currently uniquely identify a post, in the future will be required.
        For more info see https://linear.app/herohero/issue/HH-3481/add-postid-to-assettimestampupdate-mutation
        """
        postId: ID
    ): GenericMutationPayload

    """
    Updates a notification.
    """
    notificationUpdate(id: ID!, input: NotificationUpdateInput!): GenericMutationPayload

    """
    Marks every user's notification as seen
    """
    notificationMarkAllSeen: GenericMutationPayload

    """
    Create a subscribe request to a creator
    """
    subscribeRequestCreate(input: SubscribeRequestCreateInput!): GenericMutationPayload

    """
    Accept a subscribe request
    """
    subscribeRequestAccept(input: SubscribeRequestAcceptInput!): GenericMutationPayload

    """
    Add given post to user's library. User must subscribe post's creator.
    """
    postAddToLibrary(postId: ID!): PostAddToLibraryPayload!

    """
    Remove saved post from user's library.
    """
    postRemoveFromLibrary(postId: ID!): GenericMutationPayload

    """
    Delete a post and recursively all its comments
    """
    postDelete(postId: ID!): GenericMutationPayload

    """
    Create a post
    """
    postCreate(attributes: PostCreateInput!): PostCreatePayload!

    """
    Updates a post with given id
    """
    postUpdate(postId: ID!, attributes: PostUpdateInput!): PostUpdatePayload!

    """
    Create a new category with given name
    """
    categoryCreate(input: CategoryCreateInput!): CategoryPayload!

    """
    Update existing category
    """
    categoryUpdate(input: CategoryUpdateInput!, id: String!): CategoryPayload!

    """
    Delete existing category
    """
    categoryDelete(categoryId: ID!): GenericMutationPayload!

    """
    Order categories. Categories will be ordered in the same order as the passed ids.
    """
    categoriesOrder(input: CategoriesOrderInput!): GenericMutationPayload

    """
    Creates a comment under given parent, can be either post or another comment.
    Only two levels of comments are supported.
    """
    commentCreate(parentId: ID!, siblingId: ID, attributes: CommentAttributesInput!): CommentCreatePayload!

    """
    Updates given comment. User can only update comments he created
    """
    commentUpdate(commentId: ID!, attributes: CommentAttributesInput!): CommentUpdatePayload!

    """
    Delete a comment and recursively all children
    """
    commentDelete(commentId: ID!): GenericMutationPayload

    """
    Cancel user's subscription to the given creator
    """
    subscriptionCancel(creatorId: ID!): SubscriptionDeletePayload!

    """
    Renew user's subscription to the given creator
    """
    subscriptionRenew(creatorId: ID!): SubscriptionRenewPayload!

    """
    Cancel given creator's subscriber subscription. Can only be called by creators for their subscribers.
    """
    subscriberDelete(subscriberId: ID!): SubscriptionDeletePayload!

    """
    Currently revokes all sessions except the current one.
    """
    sessionRevoke: GenericMutationPayload

    """
    Updates user's information.
    - Bio: Less than 1500 chars
    - Path: Longer than 2 chars, must match [a-z0-9]+, must not be one of:
    "assets", "create", "hero", "herohero", "login", "post", "public", "search", "services".
    - Name: Longer than 2 chars
    """
    userDetailsUpdate(userDetails: UserDetailsUpdateInput!): UserDetailsUpdatePayload! @deprecated

    """
    Updates user's information.
    - Bio: Less than 1500 chars
    - Path: Longer than 2 chars, must match [a-z0-9]+, must not be one of:
    "assets", "create", "hero", "herohero", "login", "post", "public", "search", "services".
    - Name: Longer than 2 chars
    """
    viewerUpdate(userDetails: UserDetailsUpdateInput!): UserDetailsUpdatePayload!

    """
    Generates a RSS feed url for the user.
    """
    rssFeedUrlGenerate(creatorId: String!): RssFeedUrlGeneratePayload!

    """
    Update user's notification settings, null or undefined values will be replaced with current values
    """
    notificationSettingsUpdate(input: NotificationSettingsUpdateInput!): NotificationSettingsUpdatePayload!
}

input SearchPostsFilter {
    query: String
}

type NotificationSettingsUpdatePayload {
    success: Boolean!
}

input NotificationSettingsUpdateInput {
    emailNewPost: Boolean
    emailNewDm: Boolean
    pushNewPost: Boolean
    pushNewComment: Boolean
    newsletter: Boolean
    termsChanged: Boolean
}

type CategoryPayload {
    category: Category!
}

input CategoriesOrderInput {
    categoryIds: [String!]!
}

input CategoryUpdateInput {
    name: String!
}

input CategoryCreateInput {
    name: String!
    index: Int = 0
}

input PostUpdateInput {
    text: String
    textHtml: String
    textDelta: String
    categories: [String!]
    assets: [PostAssetInput!]
    "Cannot be in the past"
    publishedAt: DateTime
    pinnedAt: DateTime
    isSponsored: Boolean
    isAgeRestricted: Boolean
    isExcludedFromRss: Boolean
}

input PostCreateInput {
    text: String!
    textHtml: String!
    textDelta: String
    "Cannot be in the past"
    publishedAt: DateTime
    categories: [String!]!
    assets: [PostAssetInput!]!
    isSponsored: Boolean
    isAgeRestricted: Boolean
}

type ExpectedIncome {
    grossIncomeCents: Int!
    netIncomeCents: Int!
}

type RssFeedUrlGeneratePayload {
    rssFeedUrl: RssFeedUrl
}

type RssFeedUrl {
    url: String!
}

type PostAddToLibraryPayload {
    savedPost: SavedPost
}

type PostCreatePayload {
    post: CompleteContentPost
}

type PostUpdatePayload {
    post: CompleteContentPost
}

type CommentCreatePayload {
    comment: Comment
}

type CommentUpdatePayload {
    comment: Comment
}

type UserDetailsUpdatePayload {
    userDetails: UserDetails
    errors: [UserDetailsUpdateError]
}

type UserDetailsUpdateError {
    property: String!
    value: String!
    errorType: UserDetailsUpdateErrorType!
}

enum UserDetailsUpdateErrorType {
    MIN_LENGTH_TWO
    LOWERCASE_ALPHANUMERIC
    ILLEGAL_STRING
    PATH_TAKEN
    PATH_CHANGE_TOO_OFTEN
    MAX_LENGTH_EXCEEDED
}

input UserImageInput {
    url: String!
    width: Int!
    height: Int!
}

input UserDetailsUpdateInput {
    name: String
    bio: String
    path: String
    profileImage: UserImageInput
    isOfAge: Boolean
}

input CommentAttributesInput {
    text: String!
    textHtml: String!
    textDelta: String
    assets: [PostAssetInput!]!
}

input SavedPostsFilter {
    subscribedCreatorsOnly: Boolean
}

input SubscriptionFilter {
    "Default is false"
    expired: Boolean
}

type GenericMutationPayload {
    "Whether the operation was successful."
    success: Boolean!
}

type SubscriptionDeletePayload {
    success: Boolean!
    "Deleted subscription"
    subscription: UserSubscriptionDetails!
}

type SubscriptionRenewPayload {
    success: Boolean!
    "Renewed subscription"
    subscription: UserSubscriptionDetails!
}

"""
For some reason there is no input union, so we have to resort to this solution.
At least one must be set, otherwise validation error is thrown.
We must wait for https://github.com/graphql/graphql-spec/issues/488 to be implemented.
"""
input PostAssetInput {
    image: PostImageAssetInput
    gjirafa: PostGjirafaAssetInput
    gjirafaLivestream: PostGjirafaLivestreamAssetInput
    document: PostDocumentAssetInput
}

input PostImageAssetInput {
    url: String!
    width: Int!
    height: Int!
}

input PostGjirafaAssetInput {
    id: String!
    thumbnailUrl: String
}

input PostGjirafaLivestreamAssetInput {
    id: String!
    thumbnailUrl: String
}

input PostDocumentAssetInput {
    url: String!
    type: PostDocumentType!
    name: String!
    thumbnailUrl: String
}

input NotificationUpdateInput {
    "The time when notification was marked as checked."
    checkedAt: DateTime
    "The time when notification was marked as seen."
    seenAt: DateTime
}

input SubscribeRequestCreateInput {
    "ID of the creator to subscribe to"
    creatorId: ID!
}

input SubscribeRequestAcceptInput {
    "ID of the subscribe request to accept"
    id: ID!
}

interface Notification {
    id: ID!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    "This should be the id of the entity or event that triggered this notification"
    targetId: ID @deprecated(reason: "Will be deleted, no reason for this to be here")
}

"""
Notifications for new posts
"""
type PostNotification implements Notification {
    id: ID!
    type: PostNotificationType!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    "Id of the post that triggered this notification"
    targetId: ID @deprecated(reason: "Will be deleted, use `postId` instead")
    postId: ID!
    "Creator of the post"
    creator: User

    "Post that triggered this notification"
    post: Post
}

enum PostNotificationType {
    NEW_POST
    NEW_LIVESTREAM
}

"""
Notifications for new subscribers.
"""
type SubscriberNotification implements Notification {
    id: ID!
    type: SubscriberNotificationType!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    "Currently always null, we might want to change this to id of the last subscriber"
    targetId: ID @deprecated(reason: "Will be deleted since it's always null")

    "Last subscriber that associated with this notification"
    lastSubscriber: User
}

enum SubscriberNotificationType {
    NEW_SUBSCRIPTION
}

"""
Notifications for subscription related events, such as failed payments
when subscribing.
"""
type SubscriptionNotification implements Notification {
    id: ID!
    type: SubscriptionNotificationType!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    "Currently always null, we might want to change this to id of the creator"
    targetId: ID @deprecated(reason: "Will be deleted since it's always null")

    """
    Creator that user subscribed or attempted to subscribe to.
    """
    creator: User
}

enum SubscriptionNotificationType {
    CANCELLED_SUBSCRIPTION_ENDED
    CANCELLED_SUBSCRIPTION_INSUFFICIENT_FUNDS
    CANCELLED_SUBSCRIPTION_REFUSED
    CANCELLED_SUBSCRIPTION_REFUNDED
    CANCELLED_SUBSCRIPTION_BY_CREATOR
    CANCELLED_SUBSCRIPTION_OTHER
    PAYMENT_CARD_DECLINED
    PAYMENT_INSUFFICIENT_FUNDS
    PAYMENT_FAILED
}

"""
Notification for comments and replies.
"""
type CommentNotification implements Notification {
    id: ID!
    type: CommentNotificationType!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    "Id of the comment. Use query `comment` to fetch this comment entity"
    targetId: ID @deprecated(reason: "Will be deleted, used `commentId` instead")
    commentId: ID!
    postId: ID!
    "User that commented"
    commenter: User!
    "The post which this comment belongs"
    post: Post! @deprecated(reason: "Use postId instead")
}

enum CommentNotificationType {
    NEW_COMMENT
    NEW_REPLY
    NEW_REPLY_TO_REPLY
}

"""
Notification for messages.
"""
type MessageNotification implements Notification {
    id: ID!
    type: MessageNotificationType!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    "Id of the message that trigger this notification"
    targetId: ID @deprecated(reason: "Will be deleted, use messageId instead")
    messageId: ID!
    messageThreadId: ID!
    "Message that triggered this notification"
    message: Message

    "User that triggered this notification, for example user that bought a paid message"
    user: User!
}

enum MessageNotificationType {
    PAID_POST
}

"""
Generic notification that does not have specific variant.
"""
type GenericNotification implements Notification {
    id: ID!
    type: String!
    actorCount: Int!
    createdAt: DateTime!
    seenAt: DateTime
    checkedAt: DateTime
    targetId: ID
}

type NotificationConnection {
    "A list of nodes with data."
    nodes: [Notification!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

"""
Represents a post created by a creator that user can completely view.
"""
type CompleteContentPost implements Post {
    id: ID!
    publishedAt: DateTime!
    pinnedAt: DateTime
    state: PostState!
    text: String
    textHtml: String
    textDelta: String @deprecated(reason: "Will be deleted, no reason for this to be here")
    markdown: String
    counts: PostCounts!
    assets: [PostAsset!]!
    comments(
        "Returns up to the first n elements from the list."
        first: Int
        "Direction of sorting"
        sortDirection: SortDirection
    ): CommentConnection!
    "Author of the post"
    user: User!
    categories: [Category!]!
    savedPostInfo: SavedPostInfo
    isAgeRestricted: Boolean!
    isSponsored: Boolean!
    "Is not null only for author of the post"
    isExcludedFromRss: Boolean
}

"""
Represents a creator's post that user cannot view since he does not subscribe the creator
"""
type LimitedContentPost implements Post {
    id: ID!
    publishedAt: DateTime!
    pinnedAt: DateTime
    state: PostState!
    counts: PostCounts!
    "Author of the post"
    user: User!
    categories: [Category!]!
    savedPostInfo: SavedPostInfo
}

interface Post {
    id: ID!
    publishedAt: DateTime!
    pinnedAt: DateTime
    state: PostState!
    counts: PostCounts!
    "Author of the post"
    user: User!
    "Post categories, in some cases this can be incorrectly empty, for example when fetching posts from library"
    categories: [Category!]!
    savedPostInfo: SavedPostInfo
}

type PostConnection {
    "A list of nodes with data."
    nodes: [Post!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

input NotificationFilter {
    categories: [NotificationTypeCategory!]
}

enum NotificationTypeCategory {
    POST
    COMMENT
    SUBSCRIPTION
}

"""
Post filtering options.
"""
input PostFilter {
    categoryId: String
    "If creatorId is null, posts from all subscribed creators are returned"
    creatorId: String
    type: PostTypeFilter
    "Search string to look for."
    term: String
}

enum PostTypeFilter {
    IN_PROGRESS
}

input PostSort {
    by: PostSortFields
    order: SortDirection
}

enum PostSortFields {
    """
    This is the default we are currently using on the profile page (PINNED_AT, desc).
    """
    PINNED_AT
    """
    Sorting only by PUBLISHED_AT ignores PINNED_AT.
    """
    PUBLISHED_AT
    """
    Sorts by when user watched the post. Behaves the same way as query `postsInProgress` but includes posts that
    user has not watched which are sorted by PUBLISHED_AT.
    """
    WATCHED_AT
    """
    Sorts by number of post views. Posts that have no views are sorted by PUBLISHED_AT.
    """
    VIEWS
    """
    Sort by term relevance. Term must be set in the filter.
    """
    TERM_RELEVANCE
}

type SavedPost {
    savedAt: DateTime!
    post: Post!
}

"""
Just a helper type so Post type does not have to have SavedPost type field
which results in weird object instantiations and recursive querying
"""
type SavedPostInfo {
    savedAt: DateTime!
}

type SavedPostConnection {
    "A list of nodes with data."
    nodes: [SavedPost!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

type Comment {
    id: ID!
    publishedAt: DateTime!
    state: PostState!
    text: String
    textHtml: String
    textDelta: String
    textMarkdown: String
    counts: PostCounts!
    siblingId: ID
    parent: CommentParent!
    "Post under which this comment is"
    post: Post!
    assets: [PostAsset!]!
    "User that wrote this comment"
    user: User!
}

union CommentParent = Comment | LimitedContentPost | CompleteContentPost

type CommentConnection {
    "A list of nodes with data."
    nodes: [Comment!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

"""
Represents a direct message which is attached to message thread.
"""
type Message {
    id: ID!
    sentAt: DateTime
    sentBy: User!
    text: String
    textHtml: String
    fullAssets: Boolean
    price: Int
    assets: [PostAsset!]!
    thread: MessageThread!
}

type MessageConnection {
    "A list of nodes with data."
    nodes: [Message!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

type PostCounts {
    comments: Int!
    replies: Int!
}

union PostAsset =
    | PostImageAsset
    | PostGjirafaAsset
    | PostGjirafaLivestreamAsset
    | PostDocumentAsset
    | PostEmptyAsset
    | PostYoutubeAsset

type PostImageAsset {
    url: String!
    width: Int!
    height: Int!
}

type PostYoutubeAsset {
    id: ID!
    thumbnailUrl: String
}

type PostEmptyAsset {
    "This is a dummy value since types in GraphQL must have at least one field"
    dummy: String
}

type GjirafaAssetQuality {
    quality: String!
    size: BigInt!
    duration: Float!
}

type PostGjirafaLivestreamAsset {
    id: String! @deprecated(reason: "Use gjirafaId instead")
    gjirafaId: String!
    playbackUrl: String!
    channelPublicId: String!
    status: GjirafaLivestreamStatus!
    thumbnailUrl: String
    startDateUTC: DateTime
}

enum GjirafaLivestreamStatus {
    LIVE
    OFFLINE
    PROCESSING
    INTERRUPTED
}

type PostGjirafaAsset {
    progressTillReadiness: Int!
    progressTillCompleteness: Int!
    width: Int!
    height: Int!
    key: String
    keyId: String!
    "eg.:https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/hls/master_file.m3u8"
    videoStreamUrl: String
    "eg.: https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/hls/360p/index.m3u8"
    audioStreamUrl: String
    "eg.: https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag==,7991601123/encode/vjsnlpex/mp3/320kbps.mp3"
    audioStaticUrl: String
    hasAudio: Boolean!
    hasVideo: Boolean!
    id: String! @deprecated(reason: "Use gjirafaId instead")
    gjirafaId: String!
    hidden: Boolean!
    duration: Float!
    audioByteSize: Int
    status: GjirafaQualityTypeStatus!
    thumbnailUrl: String
    previewAnimatedUrl: String!
    previewStaticUrl: String!
    previewStripUrl: String!
    "User's timestamp from his media store, returns null if unauthenticated or user has no timestamp for the asset"
    timestamp: Float
}

enum GjirafaQualityTypeStatus {
    PROCESSING
    COMPLETE
    "After 5 retries file can’t be encoded."
    ERROR
    "Output on the template, is not applicable for that specific input."
    NOT_APPLICABLE
    "When some input fails to be encoded."
    PARTIALLY_COMPLETED
}

type PostDocumentAsset {
    url: String!
    type: PostDocumentType!
    name: String!
    thumbnailUrl: String
}

enum PostDocumentType {
    PDF
    DOCX
    RAW
    XLSX
    PPTX
    GP5
    GPX
    M3U8
    MIDI
    EPUB
}

enum PostState {
    PROCESSING
    SCHEDULED
    PUBLISHED
    REVISION
    DELETED
}

"""
Represents an active group of messages among users in a single thread.
This message thread is going to always have at least one message.
This entity does not have any details on it
such as common creators, etc.
"""
type MessageThread {
    id: ID!
    participants: [User!]!
    createdAt: DateTime
    seenAt: DateTime
    checkedAt: DateTime
    lastMessageAt: DateTime!
    lastMessage: Message!
}

"""
Represents details of a single message thread, contains information about common creator, if the user can post, etc.
This message thread can be without any messages.
"""
type MessageThreadDetails {
    id: ID!
    participants: [User!]!
    createdAt: DateTime!
    canPost: Boolean!
    relation: SubscriptionRelationType!
    commonCreators: [String!]!
    seenAt: DateTime
    checkedAt: DateTime
    deletedAt: DateTime
    deleted: Boolean!
    archived: Boolean!
    lastMessageAt: DateTime
    lastMessage: Message
}

type MessageThreadConnection {
    "A list of nodes with data."
    nodes: [MessageThread!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

"""
Describes relation between a user and a target user
"""
enum SubscriptionRelationType {
    "Relation of user to himself"
    HIMSELF
    "User is subscribed to the target"
    IS_SUBSCRIBED_TO
    "User is subscribed to the same creator as the target"
    IS_SUBSCRIBED_TO_SAME_CREATOR
    "Is subscribed by the target"
    IS_SUBSCRIBED_BY
    "There are multiple users in the relation"
    GROUP
}

"""
Represents public information about a user or a creator.
"""
type User {
    id: ID!
    name: String!
    bio: String! @deprecated(reason: "This will no longer be a string, but a type that can resolve to markdown or html")
    bioMarkdown: String!
    path: String!
    image: ImageAsset
    hasRssFeed: Boolean!
    counts: UserCounts!
    verified: Boolean!
    subscribable: Boolean!
    tier: Tier!
    categories: [Category!]!
    isDeleted: Boolean!
    "Subscription of currently logged user for this user, null if no active subscription or user is not logged in"
    subscription: UserSubscriptionDetails
    "Subscription of this user for the currently logged user, null if he is not a subscriber or user is not logged in"
    subscriber: UserSubscriptionDetails
    privacyPolicyEnabled: Boolean!
    analytics: UserAnalytics!
    "Small creators do not have gifts allowed to prevent fraudulent behaviour"
    hasGiftsAllowed: Boolean!
    spotifyShowId: String
    subscribeRequestState: SubscribeRequestState
}

enum SubscribeRequestState {
    PENDING
    ACCEPTED
    DECLINED
}

type UserConnection {
    "A list of nodes with data."
    nodes: [User!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

"""
Represents private information about a user or a creator.
"""
type UserDetails {
    id: ID!
    name: String!
    bio: String! @deprecated(reason: "This will no longer be a string, but a type that can resolve to markdown or html")
    bioMarkdown: String!
    path: String!
    image: ImageAsset
    hasRssFeed: Boolean!
    counts: UserDetailsCounts!
    verified: Boolean!
    subscribable: Boolean!
    tier: Tier!
    categories: [Category!]!
    email: String
    discord: UserDiscordSettings
    language: String!
    role: UserRole!
    creator: CreatorDetails
    spotify: UserSpotifyDetails
    livestream: LivestreamDetails
    "If user is of age to view age restricted content"
    isOfAge: Boolean!
}

"""
Represents details about gjirafa livestreams
"""
type LivestreamDetails {
    streamUrl: String!
    streamKey: String!
    "Not sure in which cases is this field nullable"
    playbackUrl: String
}

"""
Represents spotify feed and connection details
"""
type UserSpotifyDetails {
    podcastUri: String
    isConnected: Boolean!
}

"""
Represents information about creator settings, such as currency, etc.
"""
type CreatorDetails {
    stripeAccountId: String!
    stripeAccountOnboarded: Boolean!
    stripeAccountActive: Boolean!
}

enum UserRole {
    USER
    MODERATOR
}

type Category {
    id: ID!
    name: String!
    slug: String!
}

type UserDiscordSettings {
    id: ID!
    guildId: ID
}

"""
Represents information about user's company.
"""
type UserCompany {
    name: String
    address: String
    postalCode: String
    country: String
    city: String
    id: String
    vatId: String
    additionalInfo: String
    registeredWith: String
    vatType: UserCompanyVatType
    vatRate: Int
    iban: String
    swift: String
}

enum UserCompanyVatType {
    NON_VAT_PAYER
    VAT_PAYER
    EXEMPTED_FROM_VAT
}

"""
Everything related to analytics for the given user.
"""
type UserAnalytics {
    facebookPixelId: String
    ga4Stream: String
    googleAdsConversionId: String
    googleAdsConversionLabel: String
    tiktokPixelId: String
    leadHub: String
}

"""
Everything related to user's notification settings.
"""
type NotificationSettings {
    emailNewPost: Boolean!
    emailNewDm: Boolean!
    pushNewComment: Boolean!
    pushNewPost: Boolean!
    newsletter: Boolean!
    termsChanged: Boolean!
}

"""
User can be either active or deleted.
"""
enum UserStatus {
    ACTIVE
    DELETED
}

"""
Represents public information about user's counts, such as support count, supporting count, and others.
"""
type UserCounts {
    supporters: Int!
    supportersThreshold: Int
    supporting: Int!
    posts: Int!
}

"""
Represents public information about user's counts, such as support count, supporting count, and others.
"""
type UserDetailsCounts {
    supporters: Int!
    supporting: Int!
    posts: Int!
    incomes: Int!
    incomesClean: Int!
    payments: Int!
    invoices: Int!
}

type ImageAsset {
    url: String!
    width: Int!
    height: Int!
    hidden: Boolean!
}

"""
Must be called UserSubscription since Subscription is root in GraphQL.
"""
interface UserSubscription {
    id: ID!
    subscriber: User!
    creator: User!
    subscribedAt: DateTime!
}

"""
Represents general information about subscription, publicly available.
"""
type UserSubscriptionInfo implements UserSubscription {
    id: ID!
    subscriber: User!
    creator: User!
    subscribedAt: DateTime!
}

"""
Represents detail information about subscription, is available only to subscriber or creator of the subscription.
"""
type UserSubscriptionDetails implements UserSubscription {
    id: ID!
    subscribedAt: DateTime!
    status: SubscriptionStatus!
    cancelAtPeriodEnd: Boolean!
    type: SubscriptionType!
    expires: DateTime
    "If couponMethod or couponMethod are not null, and this field is null, then the coupon is applied forever"
    couponAppliedForMonths: Int
    couponAppliedForDays: Int
    couponExpiresAt: DateTime
    couponMethod: SubscriptionCouponMethod
    couponPercentOff: Int
    "Is null if `type` field is `INVITE`"
    tier: Tier
    subscriber: User!
    creator: User!
}

enum SubscriptionCouponMethod {
    "Free invites, trials, etc."
    TRIAL
    "Gifts"
    VOUCHER
}

"""
Represents information about tier, such as price, fees and currency.
"""
type Tier {
    id: ID!
    priceCents: Int!
    currency: Currency!
    default: Boolean!
    hidden: Boolean!
}

enum Currency {
    EUR
    USD
    CZK
    GBP
    PLN
    AUD
}

enum SubscriptionStatus {
    ACTIVE
    PAST_DUE
    INACTIVE
}

enum SubscriptionType {
    INVITE
    STRIPE
}

type SubscriptionConnection {
    "A list of nodes with data."
    nodes: [UserSubscription!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}

enum SubscriptionOrderBy {
    HIGHEST_PRICE
    LOWEST_PRICE
    NEWEST
    OLDEST
    GIFTED_FIRST
}

enum FeaturedCategories {
    NEW_CREATORS
    RECENTLY_ACTIVE
    TRENDING
    POPULAR
}

type RandomizedFeaturedCreatorsPayload {
    users: [User!]!
}

scalar DateTime
scalar Void
scalar BigInt

"""
Returns information about pagination in a connection, in accordance with the Shopify pagination model, see
https://shopify.dev/docs/api/admin-graphql/2023-07/objects/PageInfo.
"""
type PageInfo {
    "The cursor corresponding to the first node"
    startCursor: String
    "The cursor corresponding to the last node"
    endCursor: String
    "Whether there are more pages to fetch following the current page"
    hasNextPage: Boolean!
}

enum SortDirection {
    ASC
    DESC
}

"""
A subscribe request represents a request from a user to subscribe to a creator.
"""
type SubscribeRequest {
    id: ID!
    "User that sent the subscribe request"
    user: User!
    createdAt: DateTime!
}

"""
A connection to a list of subscribe requests.
"""
type SubscribeRequestConnection {
    "A list of subscribe requests."
    nodes: [SubscribeRequest!]!
    "Information to aid in pagination."
    pageInfo: PageInfo!
}
